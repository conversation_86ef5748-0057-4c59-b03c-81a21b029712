class ConnectionModel {
  final String host;
  final int port;
  final String username;
  final String password;

  ConnectionModel({
    required this.host,
    required this.port,
    required this.username,
    required this.password,
  });

  Map<String, dynamic> toJson() => {
    'host': host,
    'port': port,
    'username': username,
    'password': password,
  };

  factory ConnectionModel.fromJson(Map<String, dynamic> json) => ConnectionModel(
    host: json['host'],
    port: json['port'],
    username: json['username'],
    password: json['password'],
  );
}