class HotspotProfile {
  final String id;
  final String name;
  final String htmlDirectory;

  HotspotProfile({
    required this.id,
    required this.name,
    required this.htmlDirectory,
  });

  factory HotspotProfile.fromApiResponse(String response) {
    final lines = response.split('\n');
    String id = '';
    String name = '';
    String htmlDirectory = '';

    for (String line in lines) {
      if (line.contains('=.id=')) {
        id = line.split('=.id=')[1].trim();
      } else if (line.contains('=name=')) {
        name = line.split('=name=')[1].trim();
      } else if (line.contains('=html-directory=')) {
        htmlDirectory = line.split('=html-directory=')[1].trim();
      }
    }

    return HotspotProfile(
      id: id,
      name: name,
      htmlDirectory: htmlDirectory,
    );
  }
}