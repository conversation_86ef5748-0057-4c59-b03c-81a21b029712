import 'package:flutter/material.dart';
import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart' as html_dom;
import '../models/connection_model.dart';
import '../models/hotspot_profile.dart';
import '../services/ftp_service.dart';

class HtmlEditorScreen extends StatefulWidget {
  final ConnectionModel connection;
  final HotspotProfile profile;

  const HtmlEditorScreen({
    super.key,
    required this.connection,
    required this.profile,
  });

  @override
  State<HtmlEditorScreen> createState() => _HtmlEditorScreenState();
}

class _HtmlEditorScreenState extends State<HtmlEditorScreen> {
  final FtpService _ftpService = FtpService();
  String _htmlContent = '';
  html_dom.Document? _document;
  bool _isLoading = true;
  String? _error;
  Map<String, String> _backupFiles = {};
  bool _showRawHtml = false;

  @override
  void initState() {
    super.initState();
    _loadHtmlFile();
  }

  Future<void> _loadHtmlFile() async {
    try {
      await _ftpService.connect(widget.connection);

      // إنشاء نسخة احتياطية
      try {
        final files = await _ftpService.listFiles(widget.profile.htmlDirectory);
        for (String file in files) {
          if (file.endsWith('.html') || file.endsWith('.htm')) {
            try {
              final content = await _ftpService
                  .downloadFile('${widget.profile.htmlDirectory}/$file');
              _backupFiles[file] = content;
            } catch (e) {
              // تجاهل الملفات التي لا يمكن تحميلها
              print('تعذر تحميل الملف: $file - $e');
            }
          }
        }
      } catch (e) {
        // إذا فشل في قراءة المجلد، تجاهل النسخ الاحتياطية
        print('تعذر قراءة مجلد البروفايل: $e');
      }

      // تحميل ملف login.html الموجود على الميكروتيك
      final loginPath = '${widget.profile.htmlDirectory}/login.html';

      _htmlContent = await _ftpService.downloadFile(loginPath);
      _document = html_parser.parse(_htmlContent);

      setState(() => _isLoading = false);
    } catch (e) {
      setState(() {
        if (e.toString().contains('not exist') ||
            e.toString().contains('File Remote')) {
          _error =
              'ملف login.html غير موجود في المسار:\n${widget.profile.htmlDirectory}/login.html\n\nتأكد من وجود الملف على الميكروتيك';
        } else {
          _error = 'خطأ في تحميل ملف login.html:\n${e.toString()}';
        }
        _isLoading = false;
      });
    }
  }

  Future<void> _saveChanges() async {
    if (_document == null) return;

    try {
      setState(() => _isLoading = true);

      // تحديث المحتوى من الـ document
      _htmlContent = _document!.outerHtml;
      final loginPath = '${widget.profile.htmlDirectory}/login.html';

      // رفع الملف المحدث إلى الخادم
      await _ftpService.uploadFile(loginPath, _htmlContent);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ ملف login.html بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في حفظ ملف login.html: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _editText(html_dom.Element element) {
    final controller = TextEditingController(text: element.text);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحرير النص'),
        content: TextField(
          controller: controller,
          maxLines: 3,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            hintText: 'أدخل النص الجديد',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                element.text = controller.text;
              });
              Navigator.pop(context);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _editRawHtml() {
    final controller = TextEditingController(text: _htmlContent);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحرير HTML الخام'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: TextField(
            controller: controller,
            maxLines: null,
            expands: true,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'أدخل كود HTML...',
            ),
            style: const TextStyle(fontFamily: 'monospace'),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _htmlContent = controller.text;
                _document = html_parser.parse(_htmlContent);
              });
              Navigator.pop(context);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _addTableRow(html_dom.Element table) {
    final rows = table.querySelectorAll('tr');
    if (rows.isEmpty) return;

    final firstRow = rows.first;
    final cellCount = firstRow.querySelectorAll('td, th').length;

    final newRow = html_dom.Element.tag('tr');
    for (int i = 0; i < cellCount; i++) {
      final cell = html_dom.Element.tag('td');
      cell.text = 'خلية جديدة ${i + 1}';
      newRow.append(cell);
    }

    table.append(newRow);
    setState(() {});
  }

  Widget _buildElementEditor(html_dom.Element element, int depth) {
    return Container(
      margin: EdgeInsets.only(left: depth * 16.0, bottom: 8),
      child: Card(
        child: ExpansionTile(
          title: Text('${element.localName?.toUpperCase() ?? 'ELEMENT'}'),
          subtitle: element.text.isNotEmpty
              ? Text(element.text.length > 50
                  ? '${element.text.substring(0, 50)}...'
                  : element.text)
              : null,
          children: [
            if (element.text.isNotEmpty)
              ListTile(
                leading: const Icon(Icons.edit),
                title: const Text('تحرير النص'),
                onTap: () => _editText(element),
              ),
            if (element.localName == 'table')
              ListTile(
                leading: const Icon(Icons.add),
                title: const Text('إضافة صف جديد'),
                onTap: () => _addTableRow(element),
              ),
            ...element.children
                .map((child) => _buildElementEditor(child, depth + 1)),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تحرير login.html - ${widget.profile.name}'),
        actions: [
          if (!_isLoading && _document != null) ...[
            IconButton(
              icon: const Icon(Icons.code),
              onPressed: _editRawHtml,
              tooltip: 'تحرير HTML الخام',
            ),
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveChanges,
              tooltip: 'حفظ التغييرات',
            ),
          ],
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error, size: 64, color: Colors.red),
                      const SizedBox(height: 16),
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(_error!, textAlign: TextAlign.center),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: () {
                          setState(() {
                            _error = null;
                            _isLoading = true;
                          });
                          _loadHtmlFile();
                        },
                        icon: const Icon(Icons.refresh),
                        label: const Text('إعادة تحميل الملف'),
                      ),
                    ],
                  ),
                )
              : _document == null
                  ? const Center(child: Text('فشل في تحليل ملف HTML'))
                  : SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Card(
                            color: Colors.blue.shade50,
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'معلومات الملف',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text('البروفايل: ${widget.profile.name}'),
                                  Text(
                                      'المجلد: ${widget.profile.htmlDirectory}'),
                                  Text(
                                      'النسخ الاحتياطية: ${_backupFiles.length} ملف'),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'عناصر الصفحة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          ..._document!.children.map(
                              (element) => _buildElementEditor(element, 0)),
                        ],
                      ),
                    ),
    );
  }

  @override
  void dispose() {
    _ftpService.disconnect();
    super.dispose();
  }
}
