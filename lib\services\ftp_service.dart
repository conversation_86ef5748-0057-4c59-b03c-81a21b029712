import 'dart:io';
import 'package:ftpconnect/ftpconnect.dart';
import 'package:path_provider/path_provider.dart';
import '../models/connection_model.dart';

class FtpService {
  FTPConnect? _ftpConnect;

  Future<bool> connect(ConnectionModel connection) async {
    try {
      _ftpConnect = FTPConnect(
        connection.host,
        user: connection.username,
        pass: connection.password,
        port: 21,
      );

      await _ftpConnect!.connect();
      return true;
    } catch (e) {
      throw Exception('فشل الاتصال بـ FTP: ${e.toString()}');
    }
  }

  Future<List<String>> listFiles(String directory) async {
    if (_ftpConnect == null) {
      throw Exception('غير متصل بـ FTP');
    }

    try {
      // Change to the specified directory first
      await _ftpConnect!.changeDirectory(directory);
      // List content of current directory (no parameters needed)
      final files = await _ftpConnect!.listDirectoryContent();
      return files
          .map((file) => file.name ?? '')
          .where((name) => name.isNotEmpty)
          .toList();
    } catch (e) {
      throw Exception('فشل في قراءة المجلد: ${e.toString()}');
    }
  }

  Future<String> downloadFile(String remotePath) async {
    if (_ftpConnect == null) {
      throw Exception('غير متصل بـ FTP');
    }

    try {
      // Create a temporary file to download to
      final tempDir = await getTemporaryDirectory();
      final tempFile = File(
          '${tempDir.path}/temp_download_${DateTime.now().millisecondsSinceEpoch}');

      // Download file to temporary location
      await _ftpConnect!.downloadFileWithRetry(remotePath, tempFile);

      // Read the content and delete temp file
      final content = await tempFile.readAsString();
      await tempFile.delete();

      return content;
    } catch (e) {
      throw Exception('فشل في تحميل الملف: ${e.toString()}');
    }
  }

  Future<void> uploadFile(String remotePath, String content) async {
    if (_ftpConnect == null) {
      throw Exception('غير متصل بـ FTP');
    }

    try {
      // Create a temporary file with the content
      final tempDir = await getTemporaryDirectory();
      final tempFile = File(
          '${tempDir.path}/temp_upload_${DateTime.now().millisecondsSinceEpoch}');

      // Write content to temp file
      await tempFile.writeAsString(content);

      // Upload the file with remote name
      await _ftpConnect!.uploadFileWithRetry(tempFile, pRemoteName: remotePath);

      // Clean up temp file
      await tempFile.delete();
    } catch (e) {
      throw Exception('فشل في رفع الملف: ${e.toString()}');
    }
  }

  void disconnect() {
    _ftpConnect?.disconnect();
    _ftpConnect = null;
  }
}
