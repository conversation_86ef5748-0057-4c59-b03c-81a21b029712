import 'package:ftpconnect/ftpconnect.dart';
import '../models/connection_model.dart';

class FtpService {
  FTPConnect? _ftpConnect;

  Future<bool> connect(ConnectionModel connection) async {
    try {
      _ftpConnect = FTPConnect(
        connection.host,
        user: connection.username,
        pass: connection.password,
        port: 21,
      );
      
      await _ftpConnect!.connect();
      return true;
    } catch (e) {
      throw Exception('فشل الاتصال بـ FTP: ${e.toString()}');
    }
  }

  Future<List<String>> listFiles(String directory) async {
    if (_ftpConnect == null) {
      throw Exception('غير متصل بـ FTP');
    }

    try {
      final files = await _ftpConnect!.listDirectoryContent(directory);
      return files.map((file) => file.name).toList();
    } catch (e) {
      throw Exception('فشل في قراءة المجلد: ${e.toString()}');
    }
  }

  Future<String> downloadFile(String remotePath) async {
    if (_ftpConnect == null) {
      throw Exception('غير متصل بـ FTP');
    }

    try {
      final content = await _ftpConnect!.downloadFileWithRetry(remotePath);
      return String.fromCharCodes(content);
    } catch (e) {
      throw Exception('فشل في تحميل الملف: ${e.toString()}');
    }
  }

  Future<void> uploadFile(String remotePath, String content) async {
    if (_ftpConnect == null) {
      throw Exception('غير متصل بـ FTP');
    }

    try {
      final bytes = content.codeUnits;
      await _ftpConnect!.uploadFileWithRetry(bytes, remotePath);
    } catch (e) {
      throw Exception('فشل في رفع الملف: ${e.toString()}');
    }
  }

  void disconnect() {
    _ftpConnect?.disconnect();
    _ftpConnect = null;
  }
}