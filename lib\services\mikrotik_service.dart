import 'dart:io';
import 'dart:convert';
import '../models/connection_model.dart';
import '../models/hotspot_profile.dart';

class MikrotikService {
  Socket? _socket;
  bool _isConnected = false;

  Future<bool> connect(ConnectionModel connection) async {
    try {
      _socket = await Socket.connect(connection.host, connection.port);
      _isConnected = true;
      
      // تسجيل الدخول
      await _login(connection.username, connection.password);
      return true;
    } catch (e) {
      _isConnected = false;
      throw Exception('فشل الاتصال: ${e.toString()}');
    }
  }

  Future<void> _login(String username, String password) async {
    if (_socket == null) throw Exception('غير متصل');

    // إرسال أمر تسجيل الدخول
    await _sendCommand('/login');
    await _sendCommand('=name=$username');
    await _sendCommand('=password=$password');
    
    // قراءة الرد
    final response = await _readResponse();
    if (!response.contains('!done')) {
      throw Exception('فشل تسجيل الدخول');
    }
  }

  Future<List<HotspotProfile>> getHotspotProfiles() async {
    if (!_isConnected || _socket == null) {
      throw Exception('غير متصل بالميكروتيك');
    }

    await _sendCommand('/ip/hotspot/profile/print');
    final response = await _readResponse();
    
    return _parseProfiles(response);
  }

  List<HotspotProfile> _parseProfiles(String response) {
    final profiles = <HotspotProfile>[];
    final sections = response.split('!re');
    
    for (String section in sections) {
      if (section.contains('=name=') && section.contains('=html-directory=')) {
        try {
          profiles.add(HotspotProfile.fromApiResponse(section));
        } catch (e) {
          // تجاهل الأخطاء في التحليل
        }
      }
    }
    
    return profiles;
  }

  Future<void> _sendCommand(String command) async {
    if (_socket == null) return;
    
    final data = utf8.encode(command + '\n');
    _socket!.add(data);
    await _socket!.flush();
  }

  Future<String> _readResponse() async {
    if (_socket == null) return '';
    
    final buffer = StringBuffer();
    await for (final data in _socket!) {
      final response = utf8.decode(data);
      buffer.write(response);
      
      if (response.contains('!done')) {
        break;
      }
    }
    
    return buffer.toString();
  }

  void disconnect() {
    _socket?.close();
    _socket = null;
    _isConnected = false;
  }
}