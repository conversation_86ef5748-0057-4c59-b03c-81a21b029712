import 'dart:io';
import 'dart:convert';
import '../models/connection_model.dart';
import '../models/hotspot_profile.dart';

class MikrotikService {
  Socket? _socket;
  bool _isConnected = false;

  Future<bool> connect(ConnectionModel connection) async {
    try {
      _socket = await Socket.connect(connection.host, connection.port,
          timeout: const Duration(seconds: 10));
      _isConnected = true;

      // تسجيل الدخول
      await _login(connection.username, connection.password);
      return true;
    } catch (e) {
      _isConnected = false;
      throw Exception('فشل الاتصال: ${e.toString()}');
    }
  }

  Future<void> _login(String username, String password) async {
    if (_socket == null) throw Exception('غير متصل');

    try {
      // إرسال أمر تسجيل الدخول بتنسيق Mikrotik API الصحيح
      await _sendApiCommand('/login');
      await _sendApiCommand('=name=$username');
      await _sendApiCommand('=password=$password');
      await _sendApiCommand(''); // إنهاء الأمر

      // قراءة الرد
      final response = await _readApiResponse();
      if (!response.contains('!done')) {
        throw Exception('فشل تسجيل الدخول - بيانات غير صحيحة');
      }
    } catch (e) {
      throw Exception('فشل تسجيل الدخول: ${e.toString()}');
    }
  }

  Future<List<HotspotProfile>> getHotspotProfiles() async {
    if (!_isConnected || _socket == null) {
      throw Exception('غير متصل بالميكروتيك');
    }

    await _sendApiCommand('/ip/hotspot/profile/print');
    await _sendApiCommand(''); // إنهاء الأمر
    final response = await _readApiResponse();

    return _parseProfiles(response);
  }

  List<HotspotProfile> _parseProfiles(String response) {
    final profiles = <HotspotProfile>[];
    final sections = response.split('!re');

    for (String section in sections) {
      if (section.contains('=name=') && section.contains('=html-directory=')) {
        try {
          profiles.add(HotspotProfile.fromApiResponse(section));
        } catch (e) {
          // تجاهل الأخطاء في التحليل
        }
      }
    }

    return profiles;
  }

  // إرسال أمر بتنسيق Mikrotik API
  Future<void> _sendApiCommand(String command) async {
    if (_socket == null) return;

    final length = command.length;
    final lengthBytes = _encodeLength(length);
    final commandBytes = utf8.encode(command);

    _socket!.add(lengthBytes);
    _socket!.add(commandBytes);
    await _socket!.flush();
  }

  // ترميز طول الأمر حسب بروتوكول Mikrotik
  List<int> _encodeLength(int length) {
    if (length < 0x80) {
      return [length];
    } else if (length < 0x4000) {
      return [0x80 | (length >> 8), length & 0xFF];
    } else if (length < 0x200000) {
      return [0xC0 | (length >> 16), (length >> 8) & 0xFF, length & 0xFF];
    } else if (length < 0x10000000) {
      return [
        0xE0 | (length >> 24),
        (length >> 16) & 0xFF,
        (length >> 8) & 0xFF,
        length & 0xFF
      ];
    } else {
      return [
        0xF0,
        (length >> 24) & 0xFF,
        (length >> 16) & 0xFF,
        (length >> 8) & 0xFF,
        length & 0xFF
      ];
    }
  }

  Future<String> _readApiResponse() async {
    if (_socket == null) return '';

    final buffer = StringBuffer();
    bool done = false;

    while (!done) {
      final length = await _readLength();
      if (length == 0) break;

      final data = await _readBytes(length);
      final response = utf8.decode(data);
      buffer.write(response);

      if (response == '!done' || response == '!trap' || response == '!fatal') {
        done = true;
      }
    }

    return buffer.toString();
  }

  Future<int> _readLength() async {
    final firstByte = await _readByte();

    if (firstByte < 0x80) {
      return firstByte;
    } else if (firstByte < 0xC0) {
      final secondByte = await _readByte();
      return ((firstByte & 0x7F) << 8) + secondByte;
    } else if (firstByte < 0xE0) {
      final secondByte = await _readByte();
      final thirdByte = await _readByte();
      return ((firstByte & 0x3F) << 16) + (secondByte << 8) + thirdByte;
    } else if (firstByte < 0xF0) {
      final secondByte = await _readByte();
      final thirdByte = await _readByte();
      final fourthByte = await _readByte();
      return ((firstByte & 0x1F) << 24) +
          (secondByte << 16) +
          (thirdByte << 8) +
          fourthByte;
    } else {
      final secondByte = await _readByte();
      final thirdByte = await _readByte();
      final fourthByte = await _readByte();
      final fifthByte = await _readByte();
      return (secondByte << 24) +
          (thirdByte << 16) +
          (fourthByte << 8) +
          fifthByte;
    }
  }

  Future<int> _readByte() async {
    final data = await _readBytes(1);
    return data[0];
  }

  Future<List<int>> _readBytes(int count) async {
    final bytes = <int>[];
    while (bytes.length < count) {
      await for (final data in _socket!) {
        bytes.addAll(data);
        if (bytes.length >= count) break;
      }
    }
    return bytes.take(count).toList();
  }

  void disconnect() {
    _socket?.close();
    _socket = null;
    _isConnected = false;
  }
}
