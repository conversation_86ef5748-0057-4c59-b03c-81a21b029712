import '../models/connection_model.dart';
import '../models/hotspot_profile.dart';
import 'ftp_service.dart';

class MikrotikService {
  final FtpService _ftpService = FtpService();
  bool _isConnected = false;

  Future<bool> connect(ConnectionModel connection) async {
    try {
      // تحويل بيانات الاتصال لـ FTP (المنفذ 21 للـ FTP)
      final ftpConnection = ConnectionModel(
        host: connection.host,
        port: 21, // FTP port
        username: connection.username,
        password: connection.password,
      );

      await _ftpService.connect(ftpConnection);
      _isConnected = true;
      return true;
    } catch (e) {
      _isConnected = false;
      throw Exception('فشل الاتصال: ${e.toString()}');
    }
  }

  Future<List<HotspotProfile>> getHotspotProfiles() async {
    if (!_isConnected) {
      throw Exception('غير متصل بالميكروتيك');
    }

    try {
      // محاولة قراءة مجلدات الهوت سبوت من FTP
      final files = await _ftpService.listFiles('/flash/hotspot');

      // تحويل أسماء المجلدات إلى ملفات تعريف
      final profiles = <HotspotProfile>[];
      for (int i = 0; i < files.length; i++) {
        final fileName = files[i];
        if (!fileName.startsWith('.')) {
          // تجاهل الملفات المخفية
          profiles.add(HotspotProfile(
            id: i.toString(),
            name: fileName,
            htmlDirectory: '/flash/hotspot/$fileName',
          ));
        }
      }

      return profiles;
    } catch (e) {
      throw Exception('فشل في قراءة ملفات تعريف الهوت سبوت: ${e.toString()}');
    }
  }

  void disconnect() {
    _ftpService.disconnect();
    _isConnected = false;
  }
}
